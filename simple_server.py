#!/usr/bin/env python3
"""
Simple Flask server for testing without Binance API dependencies
"""

import os
import json
import logging
from datetime import datetime, timezone
from flask import Flask, request, jsonify
from flask_cors import CORS
from flask_socketio import SocketIO, emit

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__, static_folder='frontend/dist', static_url_path='')
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'test-secret-key')

# Enable CORS for all routes
CORS(app, cors_allowed_origins="*")
socketio = SocketIO(app, 
                   cors_allowed_origins="*", 
                   async_mode='eventlet',
                   logger=True,
                   engineio_logger=True,
                   ping_interval=25,
                   ping_timeout=60)

@app.route('/')
def index():
    """Serve the frontend"""
    return app.send_static_file('index.html')

@app.route('/health')
def health():
    """Health check endpoint"""
    return jsonify({
        'status': 'ok',
        'timestamp': datetime.now(timezone.utc).isoformat(),
        'service': 'TradingView Webhook Server'
    })

@app.route('/api/status')
def get_status():
    """Get system status"""
    return jsonify({
        'success': True,
        'status': 'running',
        'timestamp': datetime.now(timezone.utc).isoformat(),
        'uptime': '0:00:00',
        'binance_connected': False,
        'websocket_connected': True,
        'emergency_stop': False
    })

@app.route('/api/balance')
def get_balance():
    """Get account balance (mock data for testing)"""
    return jsonify({
        'success': True,
        'balances': [
            {'asset': 'USDT', 'free': '1000.00', 'locked': '0.00'},
            {'asset': 'BTC', 'free': '0.01', 'locked': '0.00'},
            {'asset': 'ETH', 'free': '0.1', 'locked': '0.00'}
        ],
        'timestamp': datetime.now(timezone.utc).isoformat()
    })

@app.route('/api/prices')
def get_prices():
    """Get current prices (mock data for testing)"""
    return jsonify({
        'prices': {
            'BTCUSDT': 45000.00,
            'ETHUSDT': 3000.00,
            'BNBUSDT': 300.00
        },
        'timestamp': datetime.now(timezone.utc).isoformat(),
        'count': 3
    })

@socketio.on('connect')
def handle_connect():
    logger.info('Client connected')
    emit('status', {'connected': True, 'message': 'Connected to server'})

@socketio.on('disconnect')
def handle_disconnect():
    logger.info('Client disconnected')

@socketio.on('pnl')
def handle_pnl_message(data):
    """Handle PnL messages"""
    logger.info(f'PnL request: {data}')
    return {
        'status': 'success',
        'data': {
            'success': True,
            'pnl': [],
            'totalPnL': 0,
            'realized_pnl': 0,
            'unrealized_pnl': 0,
            'timestamp': datetime.now(timezone.utc).isoformat()
        }
    }

if __name__ == '__main__':
    port = int(os.getenv('PORT', 5000))
    
    print("=" * 60)
    print("🚀 TradingView Webhook Server (Test Mode)")
    print("=" * 60)
    print(f"Starting server on port {port}...")
    print(f"Server will be available at:")
    print(f"  http://localhost:{port}")
    print(f"  http://127.0.0.1:{port}")
    print()
    print("Press Ctrl+C to stop the server")
    print("=" * 60)
    
    try:
        socketio.run(app, host='0.0.0.0', port=port, debug=False)
    except KeyboardInterrupt:
        print("\n\nShutting down server...")
    except Exception as e:
        logger.error(f"Error starting server: {e}")
        import traceback
        traceback.print_exc()
