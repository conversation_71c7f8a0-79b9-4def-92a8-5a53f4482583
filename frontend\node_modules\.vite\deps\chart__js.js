import {
  Animation,
  Animations,
  ArcElement,
  BarController,
  BarElement,
  BasePlatform,
  BasicPlatform,
  BubbleController,
  CategoryScale,
  Chart,
  DatasetController,
  DomPlatform,
  DoughnutController,
  Element,
  Interaction,
  LineController,
  LineElement,
  LinearScale,
  LogarithmicScale,
  PieController,
  PointElement,
  PolarAreaController,
  RadarController,
  RadialLinearScale,
  Scale,
  ScatterController,
  Ticks,
  TimeScale,
  TimeSeriesScale,
  _detectPlatform,
  adapters,
  animator,
  controllers,
  defaults,
  elements,
  index,
  layouts,
  plugin_colors,
  plugin_decimation,
  plugin_legend,
  plugin_subtitle,
  plugin_title,
  plugin_tooltip,
  plugins,
  registerables,
  registry,
  scales
} from "./chunk-IZ6QEAB6.js";
import "./chunk-G3PMV62Z.js";
export {
  Animation,
  Animations,
  ArcElement,
  BarController,
  BarElement,
  BasePlatform,
  BasicPlatform,
  BubbleController,
  CategoryScale,
  Chart,
  plugin_colors as Colors,
  DatasetController,
  plugin_decimation as Decimation,
  DomPlatform,
  DoughnutController,
  Element,
  index as Filler,
  Interaction,
  plugin_legend as Legend,
  LineController,
  LineElement,
  LinearScale,
  LogarithmicScale,
  PieController,
  PointElement,
  PolarAreaController,
  RadarController,
  RadialLinearScale,
  Scale,
  ScatterController,
  plugin_subtitle as SubTitle,
  Ticks,
  TimeScale,
  TimeSeriesScale,
  plugin_title as Title,
  plugin_tooltip as Tooltip,
  adapters as _adapters,
  _detectPlatform,
  animator,
  controllers,
  defaults,
  elements,
  layouts,
  plugins,
  registerables,
  registry,
  scales
};
