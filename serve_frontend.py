#!/usr/bin/env python3
"""
Simple HTTP server to serve the built frontend
"""

import http.server
import socketserver
import os
import webbrowser
from pathlib import Path

# Change to frontend/dist directory
frontend_dist = Path(__file__).parent / "frontend" / "dist"

if not frontend_dist.exists():
    print("Frontend dist directory not found. Building frontend...")
    os.system("cd frontend && npm run build")

if frontend_dist.exists():
    os.chdir(frontend_dist)
    
    PORT = 3000
    
    class MyHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
        def end_headers(self):
            self.send_header('Cache-Control', 'no-cache, no-store, must-revalidate')
            self.send_header('Pragma', 'no-cache')
            self.send_header('Expires', '0')
            super().end_headers()
    
    with socketserver.TCPServer(("", PORT), MyHTTPRequestHandler) as httpd:
        print(f"Serving frontend at http://localhost:{PORT}")
        print("Press Ctrl+C to stop the server")
        
        # Open browser
        webbrowser.open(f"http://localhost:{PORT}")
        
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\nShutting down server...")
            httpd.shutdown()
else:
    print("Could not find or build frontend dist directory")
