/**
 * WebSocket Service for Real-time Data Updates
 * Handles connection management, reconnection, and data streaming using Socket.IO
 */

import { io, Socket } from 'socket.io-client';
import type { AccountBalance, PnLData, TradeHistoryItem } from '../types/api';

export type WebSocketEventType = 'balance' | 'trade' | 'price' | 'status' | 'pnl';

export interface PriceUpdateMessage {
  type: 'price';
  data: { symbol: string; price: number } | { [symbol: string]: number } | Array<{ symbol: string; price: number }>;
  timestamp: string;
}

export interface BalanceUpdateMessage {
  type: 'balance';
  data: AccountBalance[];
  timestamp: string;
}

export interface TradeUpdateMessage {
  type: 'trade';
  data: TradeHistoryItem | TradeHistoryItem[];
  timestamp: string;
}

export interface PnLUpdateMessage {
  type: 'pnl';
  data: PnLData | PnLData[];
  timestamp: string;
}

export type WebSocketMessage =
  | PriceUpdateMessage
  | BalanceUpdateMessage
  | TradeUpdateMessage
  | PnLUpdateMessage
  | { type: 'status'; data: { connected: boolean; message?: string }; timestamp: string };

interface WebSocketConfig {
  url: string;
  reconnectInterval: number;
  maxReconnectAttempts: number;
  heartbeatInterval: number;
  connectionTimeout: number;
  ackTimeout: number;
  maxRetryAttempts: number;
  retryBaseDelay: number;
}

type EventCallback<T> = (data: T) => void;

class WebSocketService {
  private socket: Socket | null = null;
  private config: WebSocketConfig;
  private listeners: Map<WebSocketEventType, Set<EventCallback<unknown>>> = new Map();
  private reconnectAttempts = 0;
  private isConnecting = false;
  private isConnected = false;
  private connectionCallbacks: Set<(connected: boolean) => void> = new Set();
  private subscriptions: Set<string> = new Set();
  private errorStats: Map<string, { count: number; lastError: string; lastOccurrence: number }> = new Map();
  private connectionHistory: Array<{ timestamp: number; event: string; details?: unknown }> = [];
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private lastHeartbeat: number = 0;
  private connectionQuality: 'excellent' | 'good' | 'poor' | 'disconnected' = 'disconnected';
  private lastPongTime = 0;
  private pingTimeout: NodeJS.Timeout | null = null;

  constructor(config?: Partial<WebSocketConfig>) {
    this.config = {
      url: import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000',
      reconnectInterval: 3000,
      maxReconnectAttempts: 10,
      heartbeatInterval: 25000, // Match server ping_interval (25s)
      connectionTimeout: 20000, // Increased to 20s for complex operations
      ackTimeout: 30000, // Increased to 30s for PnL calculations which can be slow
      maxRetryAttempts: 3, // Increased retries for better reliability
      retryBaseDelay: 1000, // Increased base delay for better stability
      ...config,
    };

    // Initialize event listeners map
    ['balance', 'trade', 'price', 'status', 'pnl'].forEach(type => {
      this.listeners.set(type as WebSocketEventType, new Set());
    });
  }

  /**
   * Connect to Socket.IO server
   */
  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.isConnecting || this.isConnected) {
        console.log('[WebSocket] Already connecting or connected');
        resolve();
        return;
      }

      console.log(`[WebSocket] Attempting to connect to ${this.config.url}...`);
      this.isConnecting = true;

      try {
        // Initialize socket connection with server-matching timeouts
        const socketConfig = {
          transports: ['polling', 'websocket'], // Enable both transports for better reliability
          upgrade: true, // Allow WebSocket upgrade for better performance
          timeout: this.config.connectionTimeout, // Use configured timeout (20s)
          ackTimeout: this.config.ackTimeout, // 30s timeout for acknowledgements
          reconnection: false, // Handle reconnection manually
          forceNew: true,
          autoConnect: false, // Connect manually to control timing
          withCredentials: false,
          rememberUpgrade: false,
          path: '/socket.io/',
          closeOnBeforeunload: false,
          retries: this.config.maxRetryAttempts, // Add retry configuration
        };
        
        console.log('[WebSocket] Creating socket with config:', {
          url: this.config.url,
          ...socketConfig
        });
        
        this.socket = io(this.config.url, socketConfig);
        
        // Add a small delay before connecting to avoid race conditions
        setTimeout(() => {
          if (this.socket && !this.socket.connected) {
            console.log('[WebSocket] Initiating manual connection after delay...');
            this.socket.connect();
          } else {
            console.log('[WebSocket] Socket already connected or null:', {
              exists: !!this.socket,
              connected: this.socket?.connected
            });
          }
        }, 500); // Increased delay

        console.log('[WebSocket] Socket created, setting up event handlers...');

        // Add timeout for connection attempt
        const connectionTimeout = setTimeout(() => {
          console.error(`[WebSocket] Connection timeout after ${this.config.connectionTimeout / 1000} seconds`);
          this.isConnecting = false;
          if (this.socket && !this.socket.connected) {
            console.log('[WebSocket] Cleaning up failed connection...');
            this.socket.disconnect();
            this.socket = null;
          }
          reject(new Error('Connection timeout'));
        }, this.config.connectionTimeout);

        this.socket.on('connect', () => {
          console.log('[WebSocket] Connected successfully with ID:', this.socket?.id);
          clearTimeout(connectionTimeout);
          
          // Add a small delay to ensure the connection is fully established
          setTimeout(() => {
            this.isConnected = true;
            this.isConnecting = false;
            this.reconnectAttempts = 0;
            this.connectionQuality = 'excellent';
            this.lastHeartbeat = Date.now();
            this.lastPongTime = Date.now();
            this.startHeartbeatMonitoring();
            this.startPingPong();
            this.addConnectionEvent('connected');
            this.setupEventHandlers();
            this.resubscribeToEvents();
            this.notifyConnectionChange(true);
            resolve();
          }, 100);
        });

        this.socket.on('disconnect', (reason) => {
          console.log('[WebSocket] Disconnected with reason:', reason);
          this.isConnected = false;
          this.isConnecting = false;
          this.connectionQuality = 'disconnected';
          this.stopHeartbeatMonitoring();
          this.stopPingPong();
          this.addConnectionEvent('disconnected', { reason });
          this.notifyConnectionChange(false);
          
          // Attempt reconnection for unexpected disconnects
          if (reason !== 'io client disconnect' && reason !== 'io server disconnect') {
            console.log('[WebSocket] Unexpected disconnect, attempting reconnection...');
            this.scheduleReconnect();
          }
        });

        this.socket.on('connect_error', (error) => {
          const errorMessage = error instanceof Error ? error.message : String(error);
          console.error('[WebSocket] Connection error details:', {
            message: error.message,
            description: (error as Error & { description?: string }).description,
            context: (error as Error & { context?: unknown }).context,
            type: (error as Error & { type?: string }).type,
            stack: error.stack,
            attempt: this.reconnectAttempts + 1,
            timestamp: new Date().toISOString(),
            config: {
              url: this.config.url,
              timeout: this.config.connectionTimeout,
              ackTimeout: this.config.ackTimeout
            }
          });
          
          this.logError('connection_error', errorMessage);
          this.addConnectionEvent('connect_error', { error: errorMessage, attempt: this.reconnectAttempts + 1 });
          
          clearTimeout(connectionTimeout);
          this.isConnecting = false;
          this.isConnected = false;
          this.notifyConnectionChange(false);
          
          // Clean up the socket on error
          if (this.socket) {
            this.socket.disconnect();
            this.socket = null;
          }
          
          reject(error);
        });

        // Add comprehensive Socket.IO debugging events
        this.socket.on('connecting', () => {
          console.log('[WebSocket] Socket.IO connecting event triggered');
        });

        this.socket.on('reconnect', (attemptNumber) => {
          console.log(`[WebSocket] Reconnected after ${attemptNumber} attempts`);
          this.isConnected = true;
          this.isConnecting = false;
          this.reconnectAttempts = 0;
          this.resubscribeToEvents();
          this.notifyConnectionChange(true);
        });

        this.socket.on('reconnect_attempt', (attemptNumber) => {
          console.log(`[WebSocket] Reconnection attempt ${attemptNumber}`);
        });

        this.socket.on('reconnect_error', (error) => {
          console.error('[WebSocket] Reconnection error:', error);
        });

        this.socket.on('reconnect_failed', () => {
          console.error('[WebSocket] Reconnection failed - max attempts reached');
        });

        // Add transport-specific events
        this.socket.io.on('error', (error) => {
          console.error('[WebSocket] Socket.IO engine error:', error);
        });

        this.socket.io.on('open', () => {
          console.log('[WebSocket] Socket.IO engine opened');
        });

        this.socket.io.on('close', (reason) => {
          console.log('[WebSocket] Socket.IO engine closed:', reason);
        });

              this.socket.io.on('packet', (packet: any) => { // eslint-disable-line @typescript-eslint/no-explicit-any
          console.log('[WebSocket] Socket.IO packet:', packet.type, packet.data);
        });

        this.socket.on('status', (data) => {
          console.log('[WebSocket] Status:', data);
        });

      } catch (error) {
        this.isConnecting = false;
        reject(error);
      }
    });
  }

  /**
   * Schedule a reconnection attempt with exponential backoff
   */
  private scheduleReconnect(): void {
    if (this.reconnectAttempts >= this.config.maxReconnectAttempts) {
      console.error('[WebSocket] Max reconnection attempts reached');
      return;
    }

    const backoffDelay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000);
    console.log(`[WebSocket] Scheduling reconnection in ${backoffDelay}ms (attempt ${this.reconnectAttempts + 1})`);
    
    setTimeout(() => {
      if (!this.isConnected && !this.isConnecting) {
        this.reconnectAttempts++;
        this.connect().catch((error) => {
          console.error('[WebSocket] Reconnection failed:', error);
          this.scheduleReconnect();
        });
      }
    }, backoffDelay);
  }

  /**
   * Disconnect from Socket.IO server
   */
  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }

    this.isConnected = false;
    this.isConnecting = false;
    this.reconnectAttempts = 0;
    this.subscriptions.clear();
    this.notifyConnectionChange(false);
  }

  /**
   * Subscribe to specific event type
   */
  subscribe<T extends WebSocketEventType>(eventType: T, callback: EventCallback<
    T extends 'price' ? PriceUpdateMessage['data'] :
    T extends 'balance' ? BalanceUpdateMessage['data'] :
    T extends 'trade' ? TradeUpdateMessage['data'] :
    T extends 'pnl' ? PnLUpdateMessage['data'] :
    { connected: boolean; message?: string }
  >): () => void {
    const listeners = this.listeners.get(eventType);
    if (listeners) {
      // Cast is safe because we key callbacks by eventType
      (listeners as Set<EventCallback<unknown>>).add(callback as unknown as EventCallback<unknown>);
    }

    // Subscribe to server events based on type
    if (this.socket && this.isConnected) {
      this.subscribeToServerEvent(eventType);
    }

    // Return unsubscribe function
    return () => {
      const ls = this.listeners.get(eventType);
      if (ls) {
        (ls as Set<EventCallback<unknown>>).delete(callback as unknown as EventCallback<unknown>);
      }
    };
  }

  /**
   * Subscribe to connection status changes
   */
  onConnectionChange(callback: (connected: boolean) => void): () => void {
    this.connectionCallbacks.add(callback);
    
    // Return unsubscribe function
    return () => {
      this.connectionCallbacks.delete(callback);
    };
  }

  /**
   * Send message to server
   */
  send(type: WebSocketEventType, data: unknown): Promise<unknown> {
    return new Promise((resolve, reject) => {
      // Wait for connection if currently connecting
      if (this.isConnecting && !this.isConnected) {
        console.log('[WebSocket] Waiting for connection to complete before sending message...');
        const checkConnection = () => {
          if (this.isConnected && this.socket) {
            this.sendMessage(type, data, resolve, reject);
          } else if (!this.isConnecting) {
            reject(new Error('Connection failed while waiting'));
          } else {
            setTimeout(checkConnection, 100);
          }
        };
        checkConnection();
        return;
      }

      if (!this.isConnected || !this.socket) {
        const error = new Error('Cannot send message - not connected');
        console.warn('[WebSocket]', error.message);
        reject(error);
        return;
      }

      this.sendMessage(type, data, resolve, reject);
    });
  }

  /**
   * Internal method to send message with retry logic
   */
  private sendMessage(type: WebSocketEventType, data: unknown, resolve: (value: unknown) => void, reject: (reason?: unknown) => void): void {
    this.sendMessageWithRetry(type, data, 0, resolve, reject);
  }

  /**
   * Check if connection is healthy for sending messages
   */
  private isConnectionHealthy(): boolean {
    if (!this.socket || !this.isConnected) {
      return false;
    }
    
    // Check if socket is actually connected (not just marked as connected)
    if (!this.socket.connected || this.socket.disconnected) {
      return false;
    }
    
    // Check ping-pong health - if we haven't received a pong recently, connection may be stale
    const now = Date.now();
    const pingPongTimeout = this.config.heartbeatInterval * 3; // Allow 3x heartbeat interval
    
    if (this.lastPongTime > 0 && (now - this.lastPongTime) > pingPongTimeout) {
      console.warn('[WebSocket] Connection appears stale based on ping-pong timing');
      return false;
    }
    
    // Check connection quality
    if (this.connectionQuality === 'disconnected') {
      return false;
    }
    
    return true;
  }

  /**
   * Send message with exponential backoff retry
   */
  private sendMessageWithRetry(
    type: WebSocketEventType,
    data: unknown,
    attempt: number,
    resolve: (value: unknown) => void,
    reject: (reason?: unknown) => void
  ): void {
    if (!this.socket) {
      reject(new Error('Socket not available'));
      return;
    }

    if (!this.isConnectionHealthy()) {
      reject(new Error('Socket connection is not healthy'));
      return;
    }

    const timeout = this.config.ackTimeout;

    // Use Socket.IO's built-in timeout with improved error handling
    const startTime = Date.now();
    
    this.socket.timeout(timeout).emit(type, data, (err: Error | null, response: unknown) => {
      const duration = Date.now() - startTime;
      
      if (err) {
        console.error(`[WebSocket] Message ${type} failed (attempt ${attempt + 1}) after ${duration}ms:`, err.message);
        
        // Enhanced timeout error detection
        const isTimeoutError = err.message.includes('operation has timed out') || 
                              err.message.includes('timeout') ||
                              err.message.includes('TIMEOUT') ||
                              duration >= timeout;
        
        // Check connection status before retrying
         if (!this.isConnectionHealthy()) {
           console.error(`[WebSocket] Connection lost, cannot retry ${type} message`);
           reject(new Error(`Connection lost during ${type} message`));
           return;
         }
        
        if (isTimeoutError && attempt < this.config.maxRetryAttempts) {
          // Calculate exponential backoff with jitter to prevent thundering herd
          const baseDelay = this.config.retryBaseDelay * Math.pow(2, attempt);
          const jitter = Math.random() * 0.1 * baseDelay; // 10% jitter
          const delay = Math.min(baseDelay + jitter, 10000); // Increased cap to 10s for PnL operations

          console.log(`[WebSocket] Retrying ${type} message in ${Math.round(delay)}ms (attempt ${attempt + 2}/${this.config.maxRetryAttempts + 1})`);

          setTimeout(() => {
            this.sendMessageWithRetry(type, data, attempt + 1, resolve, reject);
          }, delay);
        } else {
          // Max retries reached or non-timeout error
          const errorMsg = `Message ${type} failed permanently after ${attempt + 1} attempts: ${err.message}`;
          console.error(`[WebSocket] ${errorMsg}`);

          // For PnL messages, provide more helpful error context
          if (type === 'pnl') {
            console.error(`[WebSocket] PnL calculation may be taking longer than expected. Consider optimizing backend PnL calculation or increasing timeout values.`);
          }

          reject(new Error(errorMsg));
        }
      } else {
        console.log(`[WebSocket] Message ${type} sent successfully in ${duration}ms`, { attempt: attempt + 1, response });
        resolve(response);
      }
    });
  }

  /**
   * Get current connection status
   */
  getConnectionStatus(): { connected: boolean; connecting: boolean } {
    return {
      connected: this.isConnected,
      connecting: this.isConnecting,
    };
  }

  /**
   * Setup Socket.IO event handlers
   */
  private setupEventHandlers(): void {
    if (!this.socket) return;

    // Handle real-time updates
    this.socket.on('price_update', (data) => {
      this.handleEvent('price', data as PriceUpdateMessage['data']);
    });

    this.socket.on('balance_update', (data) => {
      this.handleEvent('balance', data as BalanceUpdateMessage['data']);
    });

    this.socket.on('pnl_update', (data) => {
      this.handleEvent('pnl', data as PnLUpdateMessage['data']);
    });

    this.socket.on('trade_update', (data) => {
      this.handleEvent('trade', data as TradeUpdateMessage['data']);
    });
  }

  /**
   * Handle incoming Socket.IO events
   */
  private handleEvent<T extends WebSocketEventType>(eventType: T, data: T extends 'price' ? PriceUpdateMessage['data'] : T extends 'balance' ? BalanceUpdateMessage['data'] : T extends 'trade' ? TradeUpdateMessage['data'] : T extends 'pnl' ? PnLUpdateMessage['data'] : { connected: boolean; message?: string }): void {
    const listeners = this.listeners.get(eventType);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          (callback as EventCallback<typeof data>)(data);
        } catch (error) {
          console.error('[WebSocket] Error in event callback:', error);
        }
      });
    }
  }

  /**
   * Subscribe to server events based on type
   */
  private subscribeToServerEvent(eventType: WebSocketEventType): void {
    if (!this.socket || !this.isConnected) return;

    const subscriptionKey = `subscribe_${eventType}`;
    if (this.subscriptions.has(subscriptionKey)) return;

    this.subscriptions.add(subscriptionKey);

    switch (eventType) {
      case 'price':
        this.socket.emit('subscribe_prices', { 
          symbols: ['BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'ADAUSDT', 'SOLUSDT'] 
        });
        break;
      case 'balance':
        this.socket.emit('subscribe_balance');
        break;
      case 'pnl':
        this.socket.emit('subscribe_pnl');
        break;
      case 'trade':
        // Trade updates are handled automatically
        break;
      case 'status':
        // Connection status is internal
        break;
    }
  }

  /**
   * Resubscribe to all active events after reconnection
   */
  private resubscribeToEvents(): void {
    this.subscriptions.clear();
    
    // Resubscribe to all event types that have listeners
    this.listeners.forEach((listeners, eventType) => {
      if (listeners.size > 0) {
        this.subscribeToServerEvent(eventType);
      }
    });
  }

  /**
   * Notify connection status change to subscribers
   */
  private notifyConnectionChange(connected: boolean): void {
    this.connectionCallbacks.forEach(callback => {
      try {
        callback(connected);
      } catch (error) {
        console.error('[WebSocket] Error in connection callback:', error);
      }
    });
  }

  /**
   * Log error statistics for monitoring
   */
  private logError(type: string, message: string): void {
    const now = Date.now();
    const existing = this.errorStats.get(type);
    
    if (existing) {
      existing.count++;
      existing.lastError = message;
      existing.lastOccurrence = now;
    } else {
      this.errorStats.set(type, {
        count: 1,
        lastError: message,
        lastOccurrence: now
      });
    }
  }

  /**
   * Add connection event to history for debugging
   */
  private addConnectionEvent(event: string, details?: unknown): void {
    this.connectionHistory.push({
      timestamp: Date.now(),
      event,
      details
    });
    
    // Keep only last 50 events
    if (this.connectionHistory.length > 50) {
      this.connectionHistory.shift();
    }
  }

  /**
   * Get error statistics for debugging
   */
  getErrorStats(): Map<string, { count: number; lastError: string; lastOccurrence: number }> {
    return new Map(this.errorStats);
  }

  /**
   * Get connection history for debugging
   */
  getConnectionHistory(): Array<{ timestamp: number; event: string; details?: unknown }> {
    return [...this.connectionHistory];
  }

  /**
   * Clear error statistics and connection history
   */
  clearDiagnostics(): void {
    this.errorStats.clear();
    this.connectionHistory.length = 0;
  }

  /**
   * Start heartbeat monitoring to track connection quality
   */
  private startHeartbeatMonitoring(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
    }

    this.heartbeatInterval = setInterval(() => {
      if (!this.socket || !this.isConnected) {
        this.stopHeartbeatMonitoring();
        return;
      }

      const now = Date.now();
      const timeSinceLastHeartbeat = now - this.lastHeartbeat;

      // Send ping to server
      this.socket.emit('ping', { timestamp: now }, (response: { timestamp: number }) => {
        if (response && response.timestamp) {
          const latency = Date.now() - response.timestamp;
          this.lastHeartbeat = Date.now();
          
          // Update connection quality based on latency
          if (latency < 100) {
            this.connectionQuality = 'excellent';
          } else if (latency < 500) {
            this.connectionQuality = 'good';
          } else {
            this.connectionQuality = 'poor';
          }
        }
      });

      // Check for connection timeout
      if (timeSinceLastHeartbeat > this.config.heartbeatInterval * 2) {
        console.warn('[WebSocket] Heartbeat timeout detected, connection may be stale');
        this.connectionQuality = 'poor';
      }
    }, this.config.heartbeatInterval);
  }

  /**
   * Stop heartbeat monitoring
   */
  private stopHeartbeatMonitoring(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  /**
   * Get current connection quality
   */
  getConnectionQuality(): 'excellent' | 'good' | 'poor' | 'disconnected' {
    return this.connectionQuality;
  }

  /**
   * Start ping-pong monitoring for connection health
   */
  private startPingPong(): void {
    if (!this.socket || !this.isConnected) return;

    // Clear any existing ping timeout
    if (this.pingTimeout) {
      clearTimeout(this.pingTimeout);
    }

    // Set up pong listener
    this.socket.on('pong', (data: { timestamp: number }) => {
      this.lastPongTime = Date.now();
      const latency = this.lastPongTime - data.timestamp;
      
      // Update connection quality based on latency
      if (latency < 100) {
        this.connectionQuality = 'excellent';
      } else if (latency < 500) {
        this.connectionQuality = 'good';
      } else {
        this.connectionQuality = 'poor';
      }
    });

    // Send periodic pings
    const sendPing = () => {
      if (!this.socket || !this.isConnected) return;
      
      const now = Date.now();
      this.socket.emit('ping', { timestamp: now });
      
      // Check if we missed a pong
      if (now - this.lastPongTime > this.config.heartbeatInterval * 2) {
        console.warn('[WebSocket] Ping-pong timeout detected, connection may be stale');
        this.connectionQuality = 'poor';
      }
      
      // Schedule next ping
      this.pingTimeout = setTimeout(sendPing, this.config.heartbeatInterval);
    };

    // Start ping cycle
    sendPing();
  }

  /**
   * Stop ping-pong monitoring
   */
  private stopPingPong(): void {
    if (this.pingTimeout) {
      clearTimeout(this.pingTimeout);
      this.pingTimeout = null;
    }
    
    if (this.socket) {
      this.socket.off('pong');
    }
  }
}

// Export singleton instance
export const webSocketService = new WebSocketService();
export default webSocketService;