#!/usr/bin/env python3
"""
Minimal backend server for testing the TradingView application
"""

import os
import sys
import json
import logging
from datetime import datetime, timezone
from flask import Flask, request, jsonify
from flask_cors import CORS
from flask_socketio import SocketIO, emit
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__, static_folder='frontend/dist', static_url_path='')
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'test-secret-key')

# Enable CORS for all routes
CORS(app, cors_allowed_origins="*")

# Initialize SocketIO
socketio = SocketIO(app, 
                   cors_allowed_origins="*", 
                   async_mode='threading',
                   logger=False,
                   engineio_logger=False,
                   ping_interval=25,
                   ping_timeout=60)

@app.route('/')
def index():
    """Serve the frontend"""
    try:
        return app.send_static_file('index.html')
    except:
        return "Frontend not built. Run 'npm run build' in the frontend directory."

@app.route('/health')
def health():
    """Health check endpoint"""
    return jsonify({
        'status': 'ok',
        'timestamp': datetime.now(timezone.utc).isoformat(),
        'service': 'TradingView Webhook Server (Minimal)'
    })

@app.route('/api/status')
def get_status():
    """Get system status"""
    return jsonify({
        'success': True,
        'status': 'running',
        'timestamp': datetime.now(timezone.utc).isoformat(),
        'uptime': '0:00:00',
        'binance_connected': False,
        'websocket_connected': True,
        'emergency_stop': False,
        'mode': 'minimal'
    })

@app.route('/api/balance')
def get_balance():
    """Get account balance (mock data for testing)"""
    return jsonify({
        'success': True,
        'balances': [
            {'asset': 'USDT', 'free': '1000.00', 'locked': '0.00'},
            {'asset': 'BTC', 'free': '0.01', 'locked': '0.00'},
            {'asset': 'ETH', 'free': '0.1', 'locked': '0.00'}
        ],
        'timestamp': datetime.now(timezone.utc).isoformat()
    })

@app.route('/api/config')
def get_config():
    """Get trading configuration"""
    return jsonify({
        'success': True,
        'config': {
            'emergency_stop': False,
            'allowed_symbols': ['BTCUSDT', 'ETHUSDT'],
            'quantity_percentage': 50,
            'stop_loss_percentage': 0,
            'take_profit_percentage': 0
        },
        'timestamp': datetime.now(timezone.utc).isoformat()
    })

@app.route('/api/prices')
def get_prices():
    """Get current prices (mock data for testing)"""
    return jsonify({
        'prices': {
            'BTCUSDT': 45000.00,
            'ETHUSDT': 3000.00,
            'BNBUSDT': 300.00
        },
        'timestamp': datetime.now(timezone.utc).isoformat(),
        'count': 3
    })

@app.route('/api/pnl')
def get_pnl():
    """Get PnL data (mock data for testing)"""
    return jsonify({
        'success': True,
        'pnl': [],
        'totalPnL': 0,
        'realized_pnl': 0,
        'unrealized_pnl': 0,
        'timestamp': datetime.now(timezone.utc).isoformat()
    })

@app.route('/api/trades')
def get_trades():
    """Get trade history (mock data for testing)"""
    return jsonify({
        'success': True,
        'trades': [],
        'total': 0,
        'timestamp': datetime.now(timezone.utc).isoformat()
    })

@socketio.on('connect')
def handle_connect():
    logger.info('Client connected')
    emit('status', {'connected': True, 'message': 'Connected to minimal server'})

@socketio.on('disconnect')
def handle_disconnect():
    logger.info('Client disconnected')

@socketio.on('balance')
def handle_balance_request(data):
    """Handle balance request"""
    logger.info(f'Balance request: {data}')
    return {
        'success': True,
        'balances': [
            {'asset': 'USDT', 'free': '1000.00', 'locked': '0.00'},
            {'asset': 'BTC', 'free': '0.01', 'locked': '0.00'},
            {'asset': 'ETH', 'free': '0.1', 'locked': '0.00'}
        ],
        'timestamp': datetime.now(timezone.utc).isoformat()
    }

@socketio.on('pnl')
def handle_pnl_request(data):
    """Handle PnL request"""
    logger.info(f'PnL request: {data}')
    return {
        'success': True,
        'pnl': [],
        'totalPnL': 0,
        'realized_pnl': 0,
        'unrealized_pnl': 0,
        'timestamp': datetime.now(timezone.utc).isoformat()
    }

if __name__ == '__main__':
    port = int(os.getenv('PORT', 5000))
    
    print("=" * 60)
    print("🚀 TradingView Webhook Server (Minimal Mode)")
    print("=" * 60)
    print(f"Starting server on port {port}...")
    print(f"Server will be available at:")
    print(f"  http://localhost:{port}")
    print(f"  http://127.0.0.1:{port}")
    print()
    print("This is a minimal version for testing purposes.")
    print("Press Ctrl+C to stop the server")
    print("=" * 60)
    
    try:
        socketio.run(app, host='0.0.0.0', port=port, debug=False, use_reloader=False)
    except KeyboardInterrupt:
        print("\n\nShutting down server...")
    except Exception as e:
        logger.error(f"Error starting server: {e}")
        import traceback
        traceback.print_exc()
