import {
  ContentView,
  <PERSON>rent<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ider,
  QueryDevtoolsContext,
  THEME_PREFERENCE,
  ThemeContext,
  createLocalStorage
} from "./chunk-GVMFLE5U.js";
import {
  createComponent,
  createMemo,
  getPreferredColorScheme
} from "./chunk-EQA7WMHC.js";
import "./chunk-G3PMV62Z.js";

// node_modules/.pnpm/@tanstack+query-devtools@5.87.3/node_modules/@tanstack/query-devtools/build/DevtoolsPanelComponent/PULY4AJ7.js
var DevtoolsPanelComponent = (props) => {
  const [localStore, setLocalStore] = createLocalStorage({
    prefix: "TanstackQueryDevtools"
  });
  const colorScheme = getPreferredColorScheme();
  const theme = createMemo(() => {
    const preference = localStore.theme_preference || THEME_PREFERENCE;
    if (preference !== "system") return preference;
    return colorScheme();
  });
  return createComponent(QueryDevtoolsContext.Provider, {
    value: props,
    get children() {
      return createComponent(PiPProvider, {
        disabled: true,
        localStore,
        setLocalStore,
        get children() {
          return createComponent(ThemeContext.Provider, {
            value: theme,
            get children() {
              return createComponent(ParentPanel, {
                get children() {
                  return createComponent(ContentView, {
                    localStore,
                    setLocalStore,
                    get onClose() {
                      return props.onClose;
                    },
                    showPanelViewOnly: true
                  });
                }
              });
            }
          });
        }
      });
    }
  });
};
var DevtoolsPanelComponent_default = DevtoolsPanelComponent;
export {
  DevtoolsPanelComponent_default as default
};
//# sourceMappingURL=PULY4AJ7-35EIGG73.js.map
