#!/usr/bin/env python3
"""
Simple test server to verify <PERSON>lask and SocketIO work
"""

from flask import Flask
from flask_socketio import SocketIO
import os

app = Flask(__name__)
app.config['SECRET_KEY'] = 'test-secret-key'

socketio = SocketIO(app, cors_allowed_origins="*")

@app.route('/')
def hello():
    return "Test server is running!"

@app.route('/health')
def health():
    return {"status": "ok"}

if __name__ == '__main__':
    print("Starting test server...")
    try:
        socketio.run(app, host='0.0.0.0', port=5000, debug=True)
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
